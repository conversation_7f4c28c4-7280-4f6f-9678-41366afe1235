import {
  Body3,
  H6,
  HeadingStrong,
  Title1Strong,
  Title2Strong,
  Title3Strong,
} from "../../../components/app-typography";

interface TrophyProps {
  project: string;
  severity: string;
  findingName: string;
  findingUrl: string;
  reconLogs: string;
  description: string;
}

export default function Trophy({
  project,
  severity,
  findingName,
  findingUrl,
  reconLogs,
  description,
}: TrophyProps) {
  return (
    <div className="mr-7 min-h-[272px] w-[300px] flex-none snap-center rounded-lg bg-back-accent-quaternary p-4 shadow-lg md:w-[370px] lg:w-[370px]">
      <div className="flex h-full flex-col justify-between">
        <div className="grid grid-cols-[3fr_1fr] items-start gap-4">
          <div>
            <H6 color="primary" className="text-left">
              {project}
            </H6>
            <Title2Strong color="primary" className="text-left">
              {severity} | {findingName}
            </Title2Strong>
            <div className="flex gap-2">
              {" "}
              <a
                href={findingUrl}
                target="_blank"
                rel="noreferrer"
                className="underline"
              >
                <Title3Strong color="accent">Finding</Title3Strong>
              </a>{" "}
              |{" "}
              <a
                href={reconLogs}
                target="_blank"
                rel="noreferrer"
                className="underline"
              >
                <Title3Strong color="accent">Recon Logs</Title3Strong>
              </a>
            </div>
          </div>
          <div className="flex items-start justify-center">
            <span className="flex size-12 items-center justify-center rounded-full bg-accent-alt-tertiary text-2xl">
              🏆
            </span>
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-center">
          <Body3 color="primary">{description}</Body3>
          <a
            href={findingUrl}
            target="_blank"
            rel="noreferrer"
            className="block underline"
          >
            <Title3Strong color="accent">BUG LINK &gt;</Title3Strong>
          </a>
        </div>
      </div>
    </div>
  );
}
