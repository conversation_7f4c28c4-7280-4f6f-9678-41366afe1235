import { memo } from "react";
import { CopyBlock } from "react-code-blocks";

import { useCodeBlockTheme } from "../services/code-block-utils";

type AppCodeProps = {
  code: string;
  language: string;
  limit?: number;
  showLineNumbers?: boolean;
  maxHeight?: number;
  extraCss?: string;
};

export const AppCode = memo(
  ({
    code,
    language,
    limit = 1500,
    showLineNumbers = true,
    maxHeight,
    extraCss = "",
  }: AppCodeProps) => {
    const theme = useCodeBlockTheme();

    return (
      <div
        className={
          maxHeight ? `mb-3 h-[700px] ${extraCss}` : `mb-3 ${extraCss}`
        }
      >
        <CopyBlock
          text={code}
          language={language}
          theme={theme}
          customStyle={{
            overflowX: "auto",
            maxHeight: `${maxHeight}px`,
          }}
          showLineNumbers={showLineNumbers}
        />
      </div>
    );
  }
);

AppCode.displayName = "AppCode";
